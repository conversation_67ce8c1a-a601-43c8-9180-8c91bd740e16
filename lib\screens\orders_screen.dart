import 'dart:async';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter/material.dart';
import 'package:wicker/services/transaction_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:wicker/widgets/order_card.dart';

class OrdersScreen extends StatefulWidget {
  const OrdersScreen({super.key});

  @override
  State<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends State<OrdersScreen> {
  final TransactionService _transactionService = TransactionService();
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounce;

  Future<List<Map<String, dynamic>>>? _ordersFuture;
  String _searchQuery = '';
  String _sortBy = 'created_at';
  String _sortOrder = 'desc';

  @override
  void initState() {
    super.initState();
    _fetchOrders();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void _fetchOrders() {
    setState(() {
      _ordersFuture = _transactionService.getMyOrders(
        searchQuery: _searchQuery,
        sortBy: _sortBy,
        sortOrder: _sortOrder,
      );
    });
  }

  Future<void> _refreshOrders() async {
    setState(() {
      _ordersFuture = _transactionService.getMyOrders();
    });
    await _ordersFuture;
  }

  void _onSearchChanged() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      if (_searchQuery != _searchController.text) {
        setState(() {
          _searchQuery = _searchController.text;
        });
        _fetchOrders();
      }
    });
  }

  void _changeSort(String newSortBy) {
    setState(() {
      if (_sortBy == newSortBy) {
        _sortOrder = _sortOrder == 'desc' ? 'asc' : 'desc';
      } else {
        _sortBy = newSortBy;
        _sortOrder = 'desc';
      }
    });
    _fetchOrders();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: const Text(
            'My Orders',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(4.0),
            child: Container(color: Colors.black, height: 3.0),
          ),
        ),
      ),
      body: Column(
        children: [
          _buildControls(),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async => _fetchOrders(),
              child: FutureBuilder<List<Map<String, dynamic>>>(
                future: _ordersFuture,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }
                  if (snapshot.hasError) {
                    return Center(child: Text('Error: ${snapshot.error}'));
                  }
                  if (!snapshot.hasData || snapshot.data!.isEmpty) {
                    return Center(
                      child: Text(
                        _searchQuery.isEmpty
                            ? 'You have not placed any orders yet.'
                            : 'No orders found for "$_searchQuery".',
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    );
                  }

                  final orders = snapshot.data!;
                  return ListView.builder(
                    padding: const EdgeInsets.only(top: 8),
                    itemCount: orders.length,
                    itemBuilder: (context, index) {
                      return OrderCard(orderData: orders[index]);
                    },
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControls() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          NeuCard(
            margin: EdgeInsets.zero,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Search by product name...',
                border: InputBorder.none,
                icon: Icon(EvaIcons.search),
              ),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildSortChip('Date', 'created_at'),
              _buildSortChip('Total', 'total_amount'),
              _buildSortChip('Status', 'status'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSortChip(String label, String sortByField) {
    final bool isSelected = _sortBy == sortByField;
    return GestureDetector(
      onTap: () => _changeSort(sortByField),
      child: NeuCard(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        backgroundColor: isSelected ? const Color(0xFF6C5CE7) : Colors.white,
        child: Row(
          children: [
            Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: isSelected ? Colors.white : Colors.black,
              ),
            ),
            if (isSelected) const SizedBox(width: 8),
            if (isSelected)
              Icon(
                _sortOrder == 'desc'
                    ? EvaIcons.arrowDownward
                    : EvaIcons.arrowUpward,
                size: 16,
                color: Colors.white,
              ),
          ],
        ),
      ),
    );
  }
}

// import 'dart:async';
// import 'package:flutter/material.dart';
// import 'package:wicker/services/transaction_service.dart';
// import 'package:wicker/widgets/order_card.dart';

// class OrdersScreen extends StatefulWidget {
//   const OrdersScreen({super.key});

//   @override
//   State<OrdersScreen> createState() => _OrdersScreenState();
// }

// class _OrdersScreenState extends State<OrdersScreen> {
//   final TransactionService _transactionService = TransactionService();
//   late Future<List<Map<String, dynamic>>> _ordersFuture;

//   @override
//   void initState() {
//     super.initState();
//     _ordersFuture = _transactionService.getMyOrders();
//   }

//   Future<void> _refreshOrders() async {
//     setState(() {
//       _ordersFuture = _transactionService.getMyOrders();
//     });
//     await _ordersFuture;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: const Color(0xFFFEF7F0),
//       appBar: PreferredSize(
//         preferredSize: const Size.fromHeight(60.0),
//         child: AppBar(
//           backgroundColor: Colors.white,
//           elevation: 0,
//           leading: IconButton(
//             icon: const Icon(Icons.arrow_back, color: Colors.black),
//             onPressed: () => Navigator.of(context).pop(),
//           ),
//           title: const Text(
//             'My Orders',
//             style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
//           ),
//           bottom: PreferredSize(
//             preferredSize: const Size.fromHeight(4.0),
//             child: Container(color: Colors.black, height: 3.0),
//           ),
//         ),
//       ),
//       body: RefreshIndicator(
//         onRefresh: _refreshOrders,
//         child: FutureBuilder<List<Map<String, dynamic>>>(
//           future: _ordersFuture,
//           builder: (context, snapshot) {
//             if (snapshot.connectionState == ConnectionState.waiting) {
//               return const Center(child: CircularProgressIndicator());
//             }
//             if (snapshot.hasError) {
//               return Center(child: Text('Error: ${snapshot.error}'));
//             }
//             if (!snapshot.hasData || snapshot.data!.isEmpty) {
//               return const Center(
//                 child: Text(
//                   'You have not placed any orders yet.',
//                   style: TextStyle(fontSize: 18, color: Colors.grey),
//                 ),
//               );
//             }

//             final orders = snapshot.data!;
//             return ListView.builder(
//               itemCount: orders.length,
//               itemBuilder: (context, index) {
//                 return OrderCard(
//                   orderData: orders[index],
//                   onUpdate: _refreshOrders, // Correctly assigned
//                 );
//               },
//             );
//           },
//         ),
//       ),
//     );
//   }
// }
